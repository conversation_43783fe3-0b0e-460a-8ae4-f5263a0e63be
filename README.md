# Návrh technologií pro modernizaci ERP systému
Pro efektivní předělání vašeho Delphi ERP systému na moderní webovou aplikaci navrhuji následující technologický stack:

## Backend
1. Django + Django REST Framework: Robustní Python framework s vynikající podporou ORM
2. PostgreSQL: Výkonná databáze s podporou JSON a vektorů (pgvector) pro AI
3. Supabase: Poskytuje autentizaci, realtime funkce a storage nad PostgreSQL

## Frontend
1. React/Next.js: Pro vytvoření responzivního a rychlého uživatelského rozhraní
2. TailwindCSS: Pro efektivní stylování
3. React Query: Pro správu stavů a cachování dat z API

## Infrastruktura
1. Docker + Kubernetes: Pro kontejnerizaci a orchestraci
2. Redis: Pro cachování a fronty úloh
3. Celery: Pro asynchronní zpracování úloh

## API a integrace
1. GraphQL (s Graphene): Flexibilní API vrstva
2. REST API endpoints: Pro standardní integraci
3. Webhooks: Implementované pomocí Django Signals

## AI připravenost
1. LangChain: Framework pro integraci LLM
2. pgvector: Pro ukládání a dotazování vektorových embedingů

# Architektura systému

## Vrstvy aplikace
1. **Databázová vrstva** - PostgreSQL + pgvector
2. **API vrstva** - Django REST Framework + GraphQL
3. **Aplikační vrstva** - Django + Celery
4. **Prezentační vrstva** - React/Next.js

## Integrace s AI
- Vektorová databáze pro ukládání embedingů
- API endpointy pro komunikaci s LLM
- Webhooks pro automatizaci procesů
